export interface Product {
    id: number;
    name: string;
    description: string;
    quantity: number;
    price: string;
    expiration: string; // Changed from number to string to match API response
    created_at: string;
    updated_at: string;
}

export interface ApiError {
    message: string;
    errors?: Record<string, string[]>;
}


// Update ProductFormData to only include editable fields
export type ProductFormData = {
    name: string;
    description: string;
    quantity: number;
    price: string;
    expiration: string;
};


// Existing types...

export interface Transaction {
    id: number;
    customer_name: string;
    customer_number?: string;
    items: {
        id: number;
        productId: number;
        productName: string;  // Changed from productName
        quantity: number;
        price: number;
        subtotal: number;
    }[];
    payment_method: 'CASH' | 'CARD';  // Changed from paymentMethod
    status: 'COMPLETED' | 'CANCELLED';
    total: string;
    created_at: string;
}


export interface TransactionItem {
    id: number;
    productId: number;
    productName: string;
    quantity: number;
    price: number;
    subtotal: number;
}

export interface SalesReport {
    totalSales: number;
    totalTransactions: number;
    averageTransactionValue: number;
    topProducts: {
        productId: number;
        productName: string;
        quantity: number;
        revenue: number;
    }[];
    dailySales: {
        date: string;
        sales: number;
        transactions: number;
    }[];
}

export interface TransactionFormData {
    customer_name: string;  // Changed from customer_name
    customer_number?: string;  // Changed from customer_number
    items: {
        productId: number; 
        quantity: number;
        price: string;
    }[];
    payment_method: 'CASH' | 'CARD';
}



export interface Forecast {
    id: number;
    product_id: number;
    forecast_type: 'SALES' | 'INVENTORY';
    forecast_date: string;
    forecasted_value: number;
    confidence_score: number;
    created_at: string;
}

export interface ForecastSummary {
    productName: string;
    currentValue: number;
    forecastedValue: number;
    percentageChange: number;
    confidenceScore: number;
}

export interface ForecastChartData {
    date: string;
    value: number;
    confidence: number;
}