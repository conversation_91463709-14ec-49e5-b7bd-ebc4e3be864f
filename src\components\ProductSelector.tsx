import { useState, useEffect } from 'react';
import {
    Autocomplete,
    TextField,
    CircularProgress
} from '@mui/material';
import { Product } from '../types';
import { api } from '../services/api';

interface ProductSelectorProps {
    selectedProduct: Product | null;
    onProductChange: (product: Product | null) => void;
}

export const ProductSelector = ({ selectedProduct, onProductChange }: ProductSelectorProps) => {
    const [products, setProducts] = useState<Product[]>([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        const fetchProducts = async () => {
            setLoading(true);
            try {
                const response = await api.getProducts();
                setProducts(response.data);
                if (response.data.length > 0 && !selectedProduct) {
                    onProductChange(response.data[0]);
                }
            } catch (error) {
                console.error('Error fetching products:', error);
            } finally {
                setLoading(false);
            }
        };
        fetchProducts();
    }, []);

    return (
        <Autocomplete
            value={selectedProduct}
            onChange={(_, newValue) => onProductChange(newValue)}
            options={products}
            getOptionLabel={(option) => option.name}
            loading={loading}
            renderInput={(params) => (
                <TextField
                    {...params}
                    label="Select Product"
                    InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                            <>
                                {loading ? <CircularProgress color="inherit" size={20} /> : null}
                                {params.InputProps.endAdornment}
                            </>
                        ),
                    }}
                />
            )}
        />
    );
};