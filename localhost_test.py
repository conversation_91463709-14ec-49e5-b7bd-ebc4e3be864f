#!/usr/bin/env python3
"""
Test localhost connectivity for frontend at http://localhost:5173
"""
import requests
import json

def test_localhost_api():
    """Test API endpoints using localhost"""
    base_url = "http://localhost:8000/api/"
    
    endpoints = [
        ("Products", "products/"),
        ("Transactions", "transactions/"),
        ("Sales Report", "reports/sales/")
    ]
    
    print("🧪 Testing Localhost API for Frontend at http://localhost:5173")
    print("=" * 60)
    
    all_success = True
    
    for name, endpoint in endpoints:
        try:
            url = base_url + endpoint
            print(f"Testing {name}: {url}")
            
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                count = len(data) if isinstance(data, list) else "object"
                print(f"✅ {name}: SUCCESS ({count} items)")
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
                all_success = False
                
        except Exception as e:
            print(f"❌ {name}: ERROR - {e}")
            all_success = False
    
    print("=" * 60)
    
    if all_success:
        print("🎉 ALL TESTS PASSED!")
        print("\n📋 Frontend Configuration:")
        print("Use this in your React/Vite app:")
        print("const API_BASE_URL = 'http://localhost:8000/api/';")
        print("\nExample fetch:")
        print("fetch('http://localhost:8000/api/products/')")
        print("  .then(response => response.json())")
        print("  .then(data => console.log(data));")
    else:
        print("❌ Some tests failed. Check Django server is running.")
    
    return all_success

if __name__ == "__main__":
    test_localhost_api()
