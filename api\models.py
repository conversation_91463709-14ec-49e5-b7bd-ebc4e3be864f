﻿from django.db import models
from django.core.validators import MinValueValidator
from datetime import date, timedelta
from decimal import Decimal
from .utils import format_currency
from django.core.validators import MinValueValidator
from .utils import format_currency
import numpy as np
from scipy.stats import linregress
from django.db.models import Sum, Avg
from django.db.models.functions import ExtractWeek, ExtractYear

class Product(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField()
    quantity = models.IntegerField(validators=[MinValueValidator(0)])
    price = models.DecimalField(
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))]  # Use Decimal instead of int
    )
    expiration = models.DateField(default=date.today() + timedelta(days=365))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def clean(self):
        from django.core.exceptions import ValidationError
        if self.expiration and self.expiration < date.today():
            raise ValidationError({'expiration': 'Expiration date cannot be in the past'})

    def save(self, *args, **kwargs):
        # Remove any currency symbols and convert to Decimal
        if isinstance(self.price, str):
            self.price = Decimal(self.price.replace('$', '').replace('₱', '').strip())
        super().save(*args, **kwargs)

    @property
    def formatted_price(self):
        return format_currency(self.price)





class Transaction(models.Model):
    PAYMENT_METHODS = [
        ('CASH', 'Cash'),
        ('CARD', 'Card'),
    ]
    
    STATUS_CHOICES = [
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]

    customer_name = models.CharField(max_length=255)
    customer_number = models.CharField(max_length=20, blank=True, null=True)
    payment_method = models.CharField(max_length=10, choices=PAYMENT_METHODS)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='COMPLETED')
    total = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class TransactionItem(models.Model):
    transaction = models.ForeignKey(Transaction, related_name='items', on_delete=models.CASCADE)
    product = models.ForeignKey('Product', on_delete=models.PROTECT)
    quantity = models.IntegerField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    subtotal = models.DecimalField(max_digits=10, decimal_places=2)

    def save(self, *args, **kwargs):
        if not self.price:
            self.price = self.product.price
        if not self.subtotal:
            self.subtotal = Decimal(str(self.quantity)) * self.price
        super().save(*args, **kwargs)



class Forecast(models.Model):
    FORECAST_TYPES = [
        ('SALES', 'Sales Forecast'),
        ('INVENTORY', 'Inventory Forecast'),
    ]

    product = models.ForeignKey('Product', on_delete=models.CASCADE)
    forecast_type = models.CharField(max_length=10, choices=FORECAST_TYPES)
    forecast_date = models.DateField()
    forecasted_value = models.DecimalField(max_digits=10, decimal_places=2)
    confidence_score = models.DecimalField(max_digits=5, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['product', 'forecast_type', 'forecast_date']

    def __str__(self):
        return f"{self.forecast_type} forecast for {self.product} on {self.forecast_date}"