<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            overflow-x: auto;
        }
        .step {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Frontend Debug Tool</h1>
        <p>This tool will help you debug the "Error fetching products" issue.</p>
        
        <div class="step">
            <h3>Step 1: Test Direct API Access</h3>
            <button onclick="testDirectAPI()">Test API Directly</button>
            <div id="direct-api-result"></div>
        </div>

        <div class="step">
            <h3>Step 2: Test with Different Methods</h3>
            <button onclick="testFetch()">Test with Fetch</button>
            <button onclick="testXHR()">Test with XMLHttpRequest</button>
            <button onclick="testAxios()">Test with Axios (if available)</button>
            <div id="method-test-result"></div>
        </div>

        <div class="step">
            <h3>Step 3: Check CORS and Network</h3>
            <button onclick="checkCORS()">Check CORS Headers</button>
            <button onclick="checkNetwork()">Check Network Connectivity</button>
            <div id="cors-result"></div>
        </div>

        <div class="step">
            <h3>Step 4: Frontend Code Examples</h3>
            <button onclick="showCodeExamples()">Show Working Code Examples</button>
            <div id="code-examples"></div>
        </div>

        <div id="debug-log"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/';
        
        function log(message, type = 'info') {
            const debugLog = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            debugLog.innerHTML += `<div class="container"><span class="${className}">[${timestamp}] ${message}</span></div>`;
        }

        async function testDirectAPI() {
            const resultDiv = document.getElementById('direct-api-result');
            resultDiv.innerHTML = '<div class="result">Testing direct API access...</div>';
            
            try {
                const response = await fetch(API_BASE_URL + 'products/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                });

                const responseText = await response.text();
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    throw new Error(`Invalid JSON response: ${responseText.substring(0, 200)}...`);
                }

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">✅ API Test Successful!
Status: ${response.status}
Products Count: ${data.length}
First Product: ${JSON.stringify(data[0], null, 2)}</div>`;
                    log('Direct API test successful', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ API Test Failed: ${error.message}</div>`;
                log(`Direct API test failed: ${error.message}`, 'error');
            }
        }

        async function testFetch() {
            const resultDiv = document.getElementById('method-test-result');
            resultDiv.innerHTML = '<div class="result">Testing with Fetch API...</div>';
            
            try {
                // Test with different fetch configurations
                const configs = [
                    { name: 'Basic Fetch', config: {} },
                    { name: 'With Headers', config: { headers: { 'Content-Type': 'application/json' } } },
                    { name: 'With CORS', config: { mode: 'cors', headers: { 'Content-Type': 'application/json' } } },
                ];

                let results = '';
                for (const { name, config } of configs) {
                    try {
                        const response = await fetch(API_BASE_URL + 'products/', config);
                        const data = await response.json();
                        results += `✅ ${name}: Success (${data.length} products)\n`;
                    } catch (error) {
                        results += `❌ ${name}: ${error.message}\n`;
                    }
                }
                
                resultDiv.innerHTML = `<div class="result">${results}</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Fetch test failed: ${error.message}</div>`;
            }
        }

        async function testXHR() {
            const resultDiv = document.getElementById('method-test-result');
            
            return new Promise((resolve) => {
                const xhr = new XMLHttpRequest();
                xhr.open('GET', API_BASE_URL + 'products/', true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        if (xhr.status === 200) {
                            try {
                                const data = JSON.parse(xhr.responseText);
                                resultDiv.innerHTML += `<div class="result success">✅ XMLHttpRequest: Success (${data.length} products)</div>`;
                                log('XMLHttpRequest test successful', 'success');
                            } catch (error) {
                                resultDiv.innerHTML += `<div class="result error">❌ XMLHttpRequest: JSON parse error</div>`;
                                log('XMLHttpRequest JSON parse failed', 'error');
                            }
                        } else {
                            resultDiv.innerHTML += `<div class="result error">❌ XMLHttpRequest: HTTP ${xhr.status}</div>`;
                            log(`XMLHttpRequest failed: HTTP ${xhr.status}`, 'error');
                        }
                        resolve();
                    }
                };
                
                xhr.onerror = function() {
                    resultDiv.innerHTML += `<div class="result error">❌ XMLHttpRequest: Network error</div>`;
                    log('XMLHttpRequest network error', 'error');
                    resolve();
                };
                
                xhr.send();
            });
        }

        async function testAxios() {
            const resultDiv = document.getElementById('method-test-result');
            
            if (typeof axios === 'undefined') {
                resultDiv.innerHTML += `<div class="result warning">⚠️ Axios not available. Include axios in your project.</div>`;
                return;
            }
            
            try {
                const response = await axios.get(API_BASE_URL + 'products/');
                resultDiv.innerHTML += `<div class="result success">✅ Axios: Success (${response.data.length} products)</div>`;
                log('Axios test successful', 'success');
            } catch (error) {
                resultDiv.innerHTML += `<div class="result error">❌ Axios: ${error.message}</div>`;
                log(`Axios test failed: ${error.message}`, 'error');
            }
        }

        async function checkCORS() {
            const resultDiv = document.getElementById('cors-result');
            resultDiv.innerHTML = '<div class="result">Checking CORS headers...</div>';
            
            try {
                const response = await fetch(API_BASE_URL + 'products/', {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                    'Content-Type': response.headers.get('Content-Type'),
                };
                
                resultDiv.innerHTML = `<div class="result">CORS Headers:
${JSON.stringify(corsHeaders, null, 2)}

Status: ${response.status}
URL: ${response.url}</div>`;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ CORS check failed: ${error.message}</div>`;
            }
        }

        async function checkNetwork() {
            const resultDiv = document.getElementById('cors-result');
            
            // Test different URLs
            const urls = [
                'http://localhost:8000/api/products/',
                'http://127.0.0.1:8000/api/products/',
                'http://localhost:8000/api/',
            ];
            
            let results = 'Network Connectivity Test:\n\n';
            
            for (const url of urls) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(url, { method: 'HEAD' });
                    const endTime = Date.now();
                    results += `✅ ${url}: ${response.status} (${endTime - startTime}ms)\n`;
                } catch (error) {
                    results += `❌ ${url}: ${error.message}\n`;
                }
            }
            
            resultDiv.innerHTML += `<div class="result">${results}</div>`;
        }

        function showCodeExamples() {
            const resultDiv = document.getElementById('code-examples');
            resultDiv.innerHTML = `
                <div class="code-block">
// React Hook Example
import { useState, useEffect } from 'react';

const useProducts = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch('http://localhost:8000/api/products/', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(\`HTTP error! status: \${response.status}\`);
        }

        const data = await response.json();
        setProducts(data);
      } catch (err) {
        console.error('Error fetching products:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  return { products, loading, error };
};

// Usage in component
const ProductList = () => {
  const { products, loading, error } = useProducts();

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      {products.map(product => (
        <div key={product.id}>{product.name}</div>
      ))}
    </div>
  );
};
                </div>
                
                <div class="code-block">
// Axios Example
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:8000/api/',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// Add request interceptor for debugging
api.interceptors.request.use(request => {
  console.log('Starting Request:', request);
  return request;
});

// Add response interceptor for debugging
api.interceptors.response.use(
  response => {
    console.log('Response:', response);
    return response;
  },
  error => {
    console.error('Response Error:', error);
    return Promise.reject(error);
  }
);

const fetchProducts = async () => {
  try {
    const response = await api.get('products/');
    return response.data;
  } catch (error) {
    console.error('Error details:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
      config: error.config
    });
    throw error;
  }
};
                </div>
            `;
        }

        // Auto-run basic test on page load
        window.onload = function() {
            log('Debug tool loaded. Backend should be running on http://localhost:8000', 'info');
            testDirectAPI();
        };
    </script>
</body>
</html>
