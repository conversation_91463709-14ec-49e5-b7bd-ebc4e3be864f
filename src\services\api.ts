import axios, { AxiosError } from 'axios';
import { Product, ProductFormData, ApiError, TransactionFormData, Transaction, SalesReport } from '../types';


const API_URL = 'http://192.168.108.108:8000/api'; 

const axiosInstance = axios.create({
    baseURL: API_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

const handleError = (error: unknown) => {
    if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError<ApiError>;
        throw axiosError.response?.data || error;
    }
    throw error;
};


export const api = {

    // Add to existing api object...

    createTransaction: async (transaction: TransactionFormData) => {
        try {
            const transformedData = {
                customer_name: transaction.customer_name,
                customer_number: transaction.customer_number,
                payment_method: transaction.payment_method,
                items: transaction.items.map(item => ({
                    product_id: item.productId,
                    quantity: item.quantity,
                    price: item.price
                }))
            };

            console.log('Sending transaction data:', JSON.stringify(transformedData, null, 2));
            const response = await axiosInstance.post<Transaction>('/transactions/', transformedData);
            return response;
        } catch (error) {
            return handleError(error as AxiosError);
        }
    },





    getTransactions: async (params?: {
        startDate?: string;
        endDate?: string;
        customerName?: string;
    }) => {
        try {
            return await axiosInstance.get<Transaction[]>('/transactions/', { params });
        } catch (error) {
            return handleError(error as AxiosError);
        }
    },

    getSalesReport: async (params?: {
        startDate?: string;
        endDate?: string;
    }) => {
        try {
            return await axiosInstance.get<SalesReport>('/reports/sales/', { params });
        } catch (error) {
            return handleError(error as AxiosError);
        }
    },









    






    getProducts: async () => {
        try {
            return await axiosInstance.get<Product[]>('/products/');
        } catch (error) {
            return handleError(error as AxiosError);
        }
    },

    getProduct: async (id: number) => {
        try {
            return await axiosInstance.get<Product>(`/products/${id}/`);
        } catch (error) {
            return handleError(error as AxiosError);
        }
    },

    createProduct: async (product: ProductFormData) => {
        try {
            return await axiosInstance.post<Product>('/products/', product);
        } catch (error) {
            return handleError(error as AxiosError);
        }
    },

    updateProduct: async (id: number, product: ProductFormData) => {
        try {
            return await axiosInstance.patch<Product>(`/products/${id}/`, product);
        } catch (error) {
            return handleError(error as AxiosError);
        }
    },


    deleteProduct: async (id: number) => {
        try {
            return await axiosInstance.delete(`/products/${id}/`);
        } catch (error) {
            return handleError(error as AxiosError);
        }
    },
};