﻿import { useState, useEffect } from 'react';
import {
    Table, TableBody, TableCell, TableContainer, TableHead,
    TableRow, Paper, Button, Typography, Stack, Alert, Snackbar,
    CircularProgress, useTheme, useMediaQuery
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { api } from '../services/api';
import { Product, ProductFormData } from '../types';
import { ProductForm } from './ProductForm';




export const ProductList = () => {
    const [products, setProducts] = useState<Product[]>([]);
    const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [alert, setAlert] = useState({ show: false, message: '', type: 'success' as 'success' | 'error' });

    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.down('md'));



    const fetchProducts = async () => {
        setIsLoading(true);
        try {
            const response = await api.getProducts();
            setProducts(response.data);
        } catch (error) {
            showAlert('Error fetching products', 'error');
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchProducts();
    }, []);

    const showAlert = (message: string, type: 'success' | 'error') => {
        setAlert({ show: true, message, type });
    };

    const handleCreateProduct = async (productData: ProductFormData) => {
        try {
            await api.createProduct(productData);
            await fetchProducts();
            setIsFormOpen(false);
            showAlert('Product created successfully', 'success');
        } catch (error) {
            showAlert('Error creating product', 'error');
        }
    };

    const handleUpdateProduct = async (productData: ProductFormData) => {
        if (!selectedProduct) return;
        try {
            await api.updateProduct(selectedProduct.id, productData);
            await fetchProducts();
            setIsFormOpen(false);
            showAlert('Product updated successfully', 'success');
        } catch (error) {
            showAlert('Error updating product', 'error');
        }
    };

    const handleDeleteProduct = async (id: number) => {
        if (!window.confirm('Are you sure you want to delete this product?')) return;
        try {
            await api.deleteProduct(id);
            await fetchProducts();
            showAlert('Product deleted successfully', 'success');
        } catch (error) {
            showAlert('Error deleting product', 'error');
        }
    };

    const openCreateForm = () => {
        setSelectedProduct(null);
        setIsFormOpen(true);
    };

    const openEditForm = (product: Product) => {
        setSelectedProduct(product);
        setIsFormOpen(true);
    };

    if (isLoading) {
        return (
            <div className="loading-state">
                <CircularProgress />
            </div>
        );
    }




    const renderMobileView = (product: Product) => (
        <div className="responsive-table-card" key={product.id}>
            <div className="table-row">
                <div className="table-cell">
                    <span className="table-cell-label">Name:</span>
                    <span>{product.name}</span>
                </div>
                <div className="table-cell">
                    <span className="table-cell-label">Description:</span>
                    <span className="cell-content-wrap">{product.description}</span>
                </div>
                <div className="table-cell">
                    <span className="table-cell-label">Quantity:</span>
                    <span className={`product-quantity ${product.quantity < 10 ? 'low-quantity' : ''}`}>
                        {product.quantity}
                    </span>
                </div>
                <div className="table-cell">
                    <span className="table-cell-label">Price:</span>
                    <span className="product-price">₱{Number(product.price).toFixed(2)}</span>
                </div>
                <div className="table-cell">
                    <span className="table-cell-label">Expiration:</span>
                    <span>{new Date(product.expiration).toLocaleDateString()}</span>
                </div>
                <div className="table-cell">
                    <Stack
                        direction={isMobile ? "column" : "row"}
                        spacing={1}
                        className="action-buttons"
                    >
                        <Button
                            fullWidth={isMobile}
                            size="small"
                            variant="contained"
                            color="primary"
                            startIcon={<EditIcon />}
                            onClick={() => openEditForm(product)}
                        >
                            Edit
                        </Button>
                        <Button
                            fullWidth={isMobile}
                            size="small"
                            variant="contained"
                            color="error"
                            startIcon={<DeleteIcon />}
                            onClick={() => handleDeleteProduct(product.id)}
                        >
                            Delete
                        </Button>
                    </Stack>
                </div>
            </div>
        </div>
    );

    const renderDesktopView = () => (
        <TableContainer component={Paper}>
            <Table>
                <TableHead>
                    <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>Description</TableCell>
                        <TableCell>Quantity</TableCell>
                        <TableCell>Price</TableCell>
                        {!isTablet && <TableCell>Expiration</TableCell>}
                        <TableCell>Actions</TableCell>
                    </TableRow>
                </TableHead>

                <TableBody>
                    {products.map((product) => (
                        <TableRow key={product.id}>
                            <TableCell>{product.name}</TableCell>
                            <TableCell className="cell-content-wrap">{product.description}</TableCell>
                            <TableCell className={`product-quantity ${product.quantity < 10 ? 'low-quantity' : ''}`}>
                                {product.quantity}
                            </TableCell>
                            <TableCell className="product-price">₱{Number(product.price).toFixed(2)}</TableCell>
                            {!isTablet && <TableCell>{new Date(product.expiration).toLocaleDateString()}</TableCell>}
                            <TableCell>
                                <Stack direction="row" spacing={1}>
                                    <Button
                                        size="small"
                                        variant="contained"
                                        color="primary"
                                        startIcon={<EditIcon />}
                                        onClick={() => openEditForm(product)}
                                    >
                                        {!isTablet && "Edit"}
                                    </Button>
                                    <Button
                                        size="small"
                                        variant="contained"
                                        color="error"
                                        startIcon={<DeleteIcon />}
                                        onClick={() => handleDeleteProduct(product.id)}
                                    >
                                        {!isTablet && "Delete"}
                                    </Button>
                                </Stack>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </TableContainer>
    );

    if (isLoading) {
        return (
            <div className="loading-state">
                <CircularProgress />
            </div>
        );
    }

    return (
        <div className="inventory-container">
            <Stack
                direction={isMobile ? "column" : "row"}
                justifyContent="space-between"
                alignItems={isMobile ? "stretch" : "center"}
                spacing={2}
                className="mb-md"
            >
                <Typography variant={isMobile ? "h5" : "h4"}>
                    Inventory Products
                </Typography>
                <Button
                    fullWidth={isMobile}
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={openCreateForm}
                    className="add-button"
                >
                    Add Product
                </Button>
            </Stack>

            {products.length === 0 ? (
                <div className="empty-state">
                    <Typography variant="h6" color="textSecondary">
                        No products found
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                        Click the "Add Product" button to create your first product
                    </Typography>
                </div>
            ) : (
                isMobile ? (
                    <div className="mobile-product-list">
                        {products.map(renderMobileView)}
                    </div>
                ) : (
                    renderDesktopView()
                )
            )}

            <ProductForm
                open={isFormOpen}
                onClose={() => setIsFormOpen(false)}
                onSubmit={selectedProduct ? handleUpdateProduct : handleCreateProduct}
                initialData={selectedProduct || undefined}
            />

            <Snackbar
                open={alert.show}
                autoHideDuration={6000}
                onClose={() => setAlert({ ...alert, show: false })}
            >
                <Alert severity={alert.type} sx={{ width: '100%' }}>
                    {alert.message}
                </Alert>
            </Snackbar>
        </div>
    );
};