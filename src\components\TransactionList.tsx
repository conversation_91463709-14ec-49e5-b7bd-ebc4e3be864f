﻿import { useState, useEffect } from 'react';
import {
    Paper, Typography, Table, TableBody, TableCell,
    TableContainer, TableHead, TableRow, Button,
    Stack, Chip
} from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import { api } from '../services/api';
import { Transaction, TransactionFormData } from '../types';
import { TransactionForm } from './TransactionForm';



export const TransactionList = () => {
    const [transactions, setTransactions] = useState<Transaction[]>([]);
    const [isFormOpen, setIsFormOpen] = useState(false);

    const fetchTransactions = async () => {
        try {
            const response = await api.getTransactions();
            setTransactions(response.data);
        } catch (error) {
            console.error('Error fetching transactions:', error);
        }
    };

    useEffect(() => {
        fetchTransactions();
    }, []);

    const handleCreateTransaction = async (data: TransactionFormData) => {
        try {
            console.log('Creating transaction with data:', JSON.stringify(data, null, 2));
            await api.createTransaction(data);
            setIsFormOpen(false);
            fetchTransactions();
        } catch (error: any) {
            const errorMessage = error.response?.data?.message || error.message || 'Failed to create transaction';
            console.error('Transaction creation failed:', {
                error,
                requestData: data,
                errorResponse: error.response?.data,
                status: error.response?.status
            });
            alert(errorMessage);
        }
    };

    const getStatusColor = (status: 'COMPLETED' | 'CANCELLED') => {
        return status === 'COMPLETED' ? 'success' : 'error';
    };

    return (
        <>
            <Stack direction="row" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h5">Transactions</Typography>
                <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => setIsFormOpen(true)}
                >
                    New Transaction
                </Button>
            </Stack>

            <TableContainer component={Paper}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>ID</TableCell>
                            <TableCell>Customer</TableCell>
                            <TableCell>Contact</TableCell>
                            <TableCell>Date</TableCell>
                            <TableCell>Items</TableCell>
                            <TableCell>Payment</TableCell>
                            <TableCell align="right">Total</TableCell>
                            <TableCell>Status</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {transactions.map((transaction) => (
                            <TableRow key={transaction.id}>
                                <TableCell>{transaction.id}</TableCell>
                                <TableCell>{transaction.customer_name}</TableCell>
                                <TableCell>{transaction.customer_number || '-'}</TableCell>
                                <TableCell>
                                    {new Date(transaction.created_at).toLocaleDateString()}
                                </TableCell>
                                <TableCell>{transaction.items.length} items</TableCell>
                                <TableCell>{transaction.payment_method}</TableCell>
                                <TableCell align="right">
                                    {transaction.total ? `₱${parseFloat(transaction.total).toFixed(2)}` : '₱0.00'}
                                </TableCell>
                                <TableCell>
                                    <Chip
                                        label={transaction.status}
                                        color={getStatusColor(transaction.status)}
                                        size="small"
                                    />
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>

            <TransactionForm
                open={isFormOpen}
                onClose={() => setIsFormOpen(false)}
                onSubmit={handleCreateTransaction}
            />
        </>
    );
};