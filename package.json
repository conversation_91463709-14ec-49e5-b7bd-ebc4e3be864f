{"name": "inventory-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.15.11", "@mui/material": "^5.15.11", "@mui/x-date-pickers": "^7.0.0", "axios": "^1.8.3", "date-fns": "^2.30.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.3.0", "recharts": "^2.15.1"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "typescript": "~5.7.2"}}