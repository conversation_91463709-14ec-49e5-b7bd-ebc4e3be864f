﻿import { useState, useEffect } from 'react';
import {
    Paper, Typography, Stack, Box, Card, CardContent,
    Table, TableBody, TableCell, TableContainer,
    TableHead, TableRow
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import { SalesReport as SalesReportType } from '../types';
import { api } from '../services/api';

export const SalesReport = () => {
    const [report, setReport] = useState<SalesReportType | null>(null);
    const [startDate, setStartDate] = useState<Date | null>(null);
    const [endDate, setEndDate] = useState<Date | null>(null);

    const fetchReport = async () => {
        try {
            const response = await api.getSalesReport({
                startDate: startDate?.toISOString(),
                endDate: endDate?.toISOString(),
            });
            setReport(response.data);
        } catch (error) {
            console.error('Error fetching sales report:', error);
        }
    };

    useEffect(() => {
        fetchReport();
    }, [startDate, endDate]);

    if (!report) return null;

    return (
        <Stack spacing={3}>
            <Stack
                direction={{ xs: 'column', md: 'row' }}
                spacing={3}
                sx={{ mb: 3 }}
            >
                <Box flex={1}>
                    <DatePicker
                        label="Start Date"
                        value={startDate}
                        onChange={(newValue: Date | null) => setStartDate(newValue)}
                        slotProps={{ textField: { fullWidth: true } }}
                    />
                </Box>
                <Box flex={1}>
                    <DatePicker
                        label="End Date"
                        value={endDate}
                        onChange={(newValue: Date | null) => setEndDate(newValue)}
                        slotProps={{ textField: { fullWidth: true } }}
                    />
                </Box>
            </Stack>

            <Stack
                direction={{ xs: 'column', md: 'row' }}
                spacing={3}
            >
                <Card sx={{ flex: 1 }}>
                    <CardContent>
                        <Typography variant="h6">Total Sales</Typography>
                        <Typography variant="h4">₱{report.totalSales.toFixed(2)}</Typography>
                    </CardContent>
                </Card>
                <Card sx={{ flex: 1 }}>
                    <CardContent>
                        <Typography variant="h6">Total Transactions</Typography>
                        <Typography variant="h4">{report.totalTransactions}</Typography>
                    </CardContent>
                </Card>
                <Card sx={{ flex: 1 }}>
                    <CardContent>
                        <Typography variant="h6">Average Transaction Value</Typography>
                        <Typography variant="h4">₱{report.averageTransactionValue.toFixed(2)}</Typography>
                    </CardContent>
                </Card>
            </Stack>

            <Paper>
                <Typography variant="h6" sx={{ p: 2 }}>Top Products</Typography>
                <TableContainer>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell>Product</TableCell>
                                <TableCell align="right">Quantity Sold</TableCell>
                                <TableCell align="right">Revenue</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {report.topProducts.map((product) => (
                                <TableRow key={product.productId}>
                                    <TableCell>{product.productName}</TableCell>
                                    <TableCell align="right">{product.quantity}</TableCell>
                                    <TableCell align="right">₱{product.revenue.toFixed(2)}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            </Paper>
        </Stack>
    );
};