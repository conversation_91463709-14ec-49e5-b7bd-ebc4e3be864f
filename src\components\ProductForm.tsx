import { useState, useEffect } from 'react';
import {
    Dialog, DialogTitle, DialogContent, DialogActions,
    Button, TextField, Stack
} from '@mui/material';
import { Product, ProductFormData } from '../types';

interface ProductFormProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (product: ProductFormData) => void | Promise<void>;
    initialData?: Product;
}



export const ProductForm = ({ open, onClose, onSubmit, initialData }: ProductFormProps) => {
    const [formData, setFormData] = useState<ProductFormData>({
        name: '',
        description: '',
        quantity: 0,
        price: '0.00',
        expiration: new Date().toISOString().split('T')[0] // Initialize with today's date
    });


    useEffect(() => {
        if (initialData) {
            setFormData({
                name: initialData.name,
                description: initialData.description,
                quantity: initialData.quantity,
                price: initialData.price,
                expiration: initialData.expiration.split('T')[0] // Format date for input
            });
        } else {
            setFormData({
                name: '',
                description: '',
                quantity: 0,
                price: '0.00',
                expiration: new Date().toISOString().split('T')[0]
            });
        }
    }, [initialData]);


    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: name === 'quantity' ? Number(value) :
                name === 'price' ? formatPrice(value) :
                    value
        }));
    };

    const formatPrice = (value: string): string => {
        // Remove any non-numeric characters except decimal point
        const numericValue = value.replace(/[^\d.]/g, '');
        // Ensure only one decimal point
        const parts = numericValue.split('.');
        if (parts.length > 2) return parts[0] + '.' + parts[1];
        // Limit to 2 decimal places
        return parts.length > 1
            ? `${parts[0]}.${parts[1].slice(0, 2)}`
            : numericValue;
    };

    return (
        <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
            <form onSubmit={(e) => {
                e.preventDefault();
                onSubmit(formData);
                onClose();
            }}>
                <DialogTitle>
                    {initialData ? 'Edit Product' : 'Add New Product'}
                </DialogTitle>
                <DialogContent>
                    <Stack spacing={2} sx={{ mt: 2 }}>
                        <TextField
                            name="name"
                            label="Product Name"
                            value={formData.name}
                            onChange={handleChange}
                            required
                            fullWidth
                        />
                        <TextField
                            name="description"
                            label="Description"
                            value={formData.description}
                            onChange={handleChange}
                            multiline
                            rows={3}
                            fullWidth
                        />
                        <TextField
                            name="quantity"
                            label="Quantity"
                            type="number"
                            value={formData.quantity}
                            onChange={handleChange}
                            required
                            fullWidth
                            
                        />
                        <TextField
                            name="price"
                            label="Price"
                            value={formData.price}
                            onChange={handleChange}
                            required
                            fullWidth
                           
                            sx={{
                                '& input': {
                                    '&::-webkit-outer-spin-button, &::-webkit-inner-spin-button': {
                                        '-webkit-appearance': 'none',
                                        margin: 0
                                    }
                                }
                            }}
                        />
                              <TextField
                            name="expiration"
                            label="Expiration Date"
                            type="date"
                            value={formData.expiration}
                            onChange={handleChange}
                            required
                            fullWidth
                          


                        />
                    </Stack>
                </DialogContent>
                <DialogActions>
                    <Button onClick={onClose}>Cancel</Button>
                    <Button type="submit" variant="contained" color="primary">
                        {initialData ? 'Update' : 'Create'}
                    </Button>
                </DialogActions>
            </form>
        </Dialog>
    );
};