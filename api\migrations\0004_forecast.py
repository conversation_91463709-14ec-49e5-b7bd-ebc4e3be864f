# Generated by Django 4.2.9 on 2025-03-16 07:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0003_transaction_transactionitem'),
    ]

    operations = [
        migrations.CreateModel(
            name='Forecast',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('forecast_type', models.CharField(choices=[('SALES', 'Sales Forecast'), ('INVENTORY', 'Inventory Forecast')], max_length=10)),
                ('forecast_date', models.DateField()),
                ('forecasted_value', models.DecimalField(decimal_places=2, max_digits=10)),
                ('confidence_score', models.DecimalField(decimal_places=2, max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='api.product')),
            ],
            options={
                'unique_together': {('product', 'forecast_type', 'forecast_date')},
            },
        ),
    ]
