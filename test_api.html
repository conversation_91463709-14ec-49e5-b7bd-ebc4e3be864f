<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Inventory Backend API Test</h1>
        <p>This page tests the connectivity to your inventory backend API.</p>
        
        <div id="connection-status"></div>
        
        <h3>API Endpoints:</h3>
        <button onclick="testProducts()">Test Products API</button>
        <button onclick="testTransactions()">Test Transactions API</button>
        <button onclick="testSalesReport()">Test Sales Report API</button>
        <button onclick="testAllEndpoints()">Test All Endpoints</button>
        
        <div id="results"></div>
    </div>

    <script>
        // Configuration
        const API_BASE_URL = 'http://***********:8000/api/';
        
        // Alternative URLs to try if the main one fails
        const FALLBACK_URLS = [
            'http://***************:8000/api/',
            'http://localhost:8000/api/',
            'http://127.0.0.1:8000/api/'
        ];

        function showStatus(message, isSuccess = true) {
            const statusDiv = document.getElementById('connection-status');
            statusDiv.innerHTML = `<div class="status ${isSuccess ? 'success' : 'error'}">${message}</div>`;
        }

        function showResult(title, data, isSuccess = true) {
            const resultsDiv = document.getElementById('results');
            const resultClass = isSuccess ? 'success' : 'error';
            const resultHtml = `
                <div class="container">
                    <h4 class="${resultClass}">${title}</h4>
                    <div class="result">${JSON.stringify(data, null, 2)}</div>
                </div>
            `;
            resultsDiv.innerHTML += resultHtml;
        }

        async function makeRequest(endpoint, baseUrl = API_BASE_URL) {
            try {
                const response = await fetch(baseUrl + endpoint, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                return { success: true, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message, status: 0 };
            }
        }

        async function testProducts() {
            showStatus('🔍 Testing Products API...', true);
            document.getElementById('results').innerHTML = '';
            
            const result = await makeRequest('products/');
            
            if (result.success) {
                showStatus('✅ Products API is working!', true);
                showResult('Products API Response', {
                    status: result.status,
                    count: result.data.length,
                    sample: result.data.slice(0, 2)
                }, true);
            } else {
                showStatus('❌ Products API failed: ' + result.error, false);
                showResult('Products API Error', result, false);
            }
        }

        async function testTransactions() {
            showStatus('🔍 Testing Transactions API...', true);
            document.getElementById('results').innerHTML = '';
            
            const result = await makeRequest('transactions/');
            
            if (result.success) {
                showStatus('✅ Transactions API is working!', true);
                showResult('Transactions API Response', {
                    status: result.status,
                    count: result.data.length,
                    sample: result.data.slice(0, 2)
                }, true);
            } else {
                showStatus('❌ Transactions API failed: ' + result.error, false);
                showResult('Transactions API Error', result, false);
            }
        }

        async function testSalesReport() {
            showStatus('🔍 Testing Sales Report API...', true);
            document.getElementById('results').innerHTML = '';
            
            const result = await makeRequest('reports/sales/');
            
            if (result.success) {
                showStatus('✅ Sales Report API is working!', true);
                showResult('Sales Report API Response', result.data, true);
            } else {
                showStatus('❌ Sales Report API failed: ' + result.error, false);
                showResult('Sales Report API Error', result, false);
            }
        }

        async function testAllEndpoints() {
            showStatus('🔍 Testing all endpoints...', true);
            document.getElementById('results').innerHTML = '';
            
            const endpoints = [
                { name: 'Products', endpoint: 'products/' },
                { name: 'Transactions', endpoint: 'transactions/' },
                { name: 'Sales Report', endpoint: 'reports/sales/' }
            ];
            
            let successCount = 0;
            
            for (const { name, endpoint } of endpoints) {
                const result = await makeRequest(endpoint);
                
                if (result.success) {
                    successCount++;
                    showResult(`✅ ${name} API`, {
                        status: result.status,
                        dataType: Array.isArray(result.data) ? `Array (${result.data.length} items)` : 'Object',
                        keys: Array.isArray(result.data) ? 'Array of items' : Object.keys(result.data)
                    }, true);
                } else {
                    showResult(`❌ ${name} API`, result, false);
                }
            }
            
            if (successCount === endpoints.length) {
                showStatus(`🎉 All ${endpoints.length} endpoints are working perfectly!`, true);
            } else {
                showStatus(`⚠️ ${successCount}/${endpoints.length} endpoints are working`, false);
            }
        }

        // Auto-test on page load
        window.onload = function() {
            showStatus('🚀 Ready to test API endpoints. Current API URL: ' + API_BASE_URL, true);
        };
    </script>
</body>
</html>
