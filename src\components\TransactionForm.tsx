﻿import { useState, useEffect } from 'react';
import {
    <PERSON>alog, DialogTitle, DialogContent, DialogActions,
    Button, TextField, Stack, Autocomplete, FormControl,
    InputLabel, Select, MenuItem, Typography
} from '@mui/material';
import { Product, TransactionFormData } from '../types';
import { api } from '../services/api';

interface TransactionFormProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (transaction: TransactionFormData) => void | Promise<void>;
}

export const TransactionForm = ({ open, onClose, onSubmit }: TransactionFormProps) => {
    const [products, setProducts] = useState<Product[]>([]);
    const [formData, setFormData] = useState<TransactionFormData>({
        customer_name: '',
        customer_number: '',
        items: [],
        payment_method: 'CASH',
    });

    // Reset form when dialog opens
    useEffect(() => {
        if (open) {
            setFormData({
                customer_name: '',
                customer_number: '',
                items: [],
                payment_method: 'CASH',
            });
        }
    }, [open]);

    useEffect(() => {
        const fetchProducts = async () => {
            try {
                const response = await api.getProducts();
                setProducts(response.data);
            } catch (error) {
                console.error('Error fetching products:', error);
            }
        };
        fetchProducts();
    }, []);

    const handleAddItem = () => {
        setFormData(prev => ({
            ...prev,
            items: [...prev.items, { productId: 0, quantity: 1, price: '0.00' }]
        }));
    };

    const handleRemoveItem = (index: number) => {
        setFormData(prev => ({
            ...prev,
            items: prev.items.filter((_, i) => i !== index)
        }));
    };

    const handleItemChange = (index: number, field: 'productId' | 'quantity', value: number) => {
        setFormData(prev => ({
            ...prev,
            items: prev.items.map((item, i) => {
                if (i === index) {
                    const updatedItem = { ...item, [field]: value };
                    // If changing product, update the price
                    if (field === 'productId') {
                        const product = products.find(p => p.id === value);
                        updatedItem.price = product ? product.price : '0.00';
                    }
                    return updatedItem;
                }
                return item;
            })
        }));
    };

    const calculateTotal = () => {
        return formData.items.reduce((total, item) => {
            const product = products.find(p => p.id === item.productId);
            return total + (product ? Number(product.price) * item.quantity : 0);
        }, 0);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Validate that all products are selected
        const hasInvalidItems = formData.items.some(item => item.productId === 0);
        if (hasInvalidItems) {
            alert('Please select products for all items');
            return;
        }
        onSubmit(formData);
    };

    return (
        <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
            <form onSubmit={handleSubmit}>
                <DialogTitle>New Transaction</DialogTitle>
                <DialogContent>
                    <Stack spacing={3} sx={{ mt: 2 }}>
                        <TextField
                            label="Customer Name"
                            value={formData.customer_name}
                            onChange={(e) => setFormData(prev => ({
                                ...prev,
                                customer_name: e.target.value
                            }))}
                            required
                            fullWidth
                        />
                        <TextField
                            label="Customer Number"
                            value={formData.customer_number}
                            onChange={(e) => setFormData(prev => ({
                                ...prev,
                                customer_number: e.target.value
                            }))}
                            fullWidth
                        />

                        <FormControl fullWidth>
                            <InputLabel>Payment Method</InputLabel>
                            <Select
                                value={formData.payment_method}
                                onChange={(e) => setFormData(prev => ({
                                    ...prev,
                                    payment_method: e.target.value as 'CASH' | 'CARD'
                                }))}
                                label="Payment Method"
                            >
                                <MenuItem value="CASH">Cash</MenuItem>
                                <MenuItem value="CARD">Card</MenuItem>
                            </Select>
                        </FormControl>

                        <div>
                            <Typography variant="h6">Items</Typography>
                            {formData.items.map((item, index) => (
                                <Stack key={index} direction="row" spacing={2} sx={{ mt: 2 }}>
                                    <Autocomplete
                                        options={products}
                                        getOptionLabel={(option) => option.name}
                                        value={products.find(p => p.id === item.productId) || null}
                                        onChange={(_, newValue) => handleItemChange(
                                            index,
                                            'productId',
                                            newValue?.id || 0
                                        )}
                                        renderInput={(params) => (
                                            <TextField
                                                {...params}
                                                label="Product"
                                                required
                                                error={item.productId === 0}
                                                helperText={item.productId === 0 ? 'Please select a product' : ''}
                                            />
                                        )}
                                        fullWidth
                                    />
                                    <TextField
                                        type="number"
                                        label="Quantity"
                                        value={item.quantity}
                                        onChange={(e) => handleItemChange(
                                            index,
                                            'quantity',
                                            Math.max(1, Number(e.target.value))
                                        )}
                                        required
                                        inputProps={{ min: 1 }}
                                        sx={{ width: 150 }}
                                    />
                                    <Button
                                        color="error"
                                        onClick={() => handleRemoveItem(index)}
                                    >
                                        Remove
                                    </Button>
                                </Stack>
                            ))}
                            <Button
                                onClick={handleAddItem}
                                sx={{ mt: 2 }}
                            >
                                Add Item
                            </Button>
                        </div>

                        <Typography variant="h6">
                            Total: ₱{calculateTotal().toFixed(2)}
                        </Typography>
                    </Stack>
                </DialogContent>
                <DialogActions>
                    <Button onClick={onClose}>Cancel</Button>
                    <Button
                        type="submit"
                        variant="contained"
                        color="primary"
                        disabled={formData.items.length === 0}
                    >
                        Complete Transaction
                    </Button>
                </DialogActions>
            </form>
        </Dialog>
    );
};