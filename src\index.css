/* ProductList.css - Professional styling for inventory management */

/* Container and general layout */
.inventory-container {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.mb-md {
    margin-bottom: 24px;
}

/* Loading and empty states */
.loading-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
}

.empty-state {
    text-align: center;
    padding: 48px 24px;
    background-color: #f9f9f9;
    border-radius: 8px;
    border: 1px dashed #e0e0e0;
}

/* Table styling */
.MuiTableContainer-root {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
    border-radius: 8px !important;
    overflow: hidden;
}

.MuiTableHead-root {
    background-color: #f5f5f5;
}

    .MuiTableHead-root .MuiTableCell-root {
        font-weight: 600;
        color: #424242;
        padding: 16px;
    }

.MuiTableBody-root .MuiTableRow-root:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.MuiTableBody-root .MuiTableCell-root {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
}

/* Content wrapping for descriptions */
.cell-content-wrap {
    max-width: 300px;
    white-space: normal;
    word-break: break-word;
}

/* Product quantity and price styling */
.product-quantity {
    font-weight: 500;
}

.low-quantity {
    color: #f44336;
    font-weight: 600;
}

.product-price {
    font-weight: 500;
    color: #1976d2;
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: 8px;
}

.add-button {
    min-width: 140px;
    height: 40px;
}

/* Mobile card view */
.mobile-product-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.responsive-table-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: transform 0.2s ease;
}

    .responsive-table-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .responsive-table-card .table-row {
        display: flex;
        flex-direction: column;
        padding: 16px;
    }

    .responsive-table-card .table-cell {
        padding: 8px 0;
        display: flex;
        flex-direction: column;
        border-bottom: 1px solid #f0f0f0;
    }

        .responsive-table-card .table-cell:last-child {
            border-bottom: none;
            margin-top: 8px;
        }

    .responsive-table-card .table-cell-label {
        font-weight: 600;
        color: #757575;
        margin-bottom: 4px;
        font-size: 0.85rem;
    }

/* Responsive adjustments */
@media (max-width: 600px) {
    .inventory-container {
        padding: 16px;
    }

    .MuiButton-root {
        padding: 8px 16px;
    }

    .MuiTypography-h4 {
        font-size: 1.5rem;
    }
}

/* Animation for alerts */
.MuiSnackbar-root {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateY(20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Form styling */
.MuiDialog-paper {
    border-radius: 12px !important;
    overflow: hidden;
}

/* Button hover effects */
.MuiButton-containedPrimary:hover {
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.MuiButton-containedError:hover {
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}
