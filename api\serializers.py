from rest_framework import serializers
from .models import Product, Transaction, TransactionItem, Forecast
from decimal import Decimal

class ProductSerializer(serializers.ModelSerializer):
    expiration = serializers.DateField(format='%Y-%m-%d', required=False)
    class Meta:
        model = Product
        fields = '__all__'

    def get_price_display(self, obj):
        return f"₱{obj.price:,.2f}"

    def validate_price(self, value):
        if isinstance(value, str):
            value = value.replace('$', '').replace('₱', '').strip()
            try:
                value = Decimal(value)
            except:
                raise serializers.ValidationError("Invalid price format")
        
        if value < 0:
            raise serializers.ValidationError("Price cannot be negative")
        return value

    def validate_expiration(self, value):
        from datetime import date
        request = self.context.get('request')
        if value is None:
            return value
        # Only enforce for create requests
        if request and request.method == 'POST' and value < date.today():
            raise serializers.ValidationError("Expiration date cannot be in the past")
        return value

class TransactionItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    subtotal = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    class Meta:
        model = TransactionItem
        fields = ['id', 'product', 'product_name', 'quantity', 'price', 'subtotal']

class TransactionSerializer(serializers.ModelSerializer):
    items = TransactionItemSerializer(many=True, read_only=True)
    
    class Meta:
        model = Transaction
        fields = [
            'id', 
            'customer_name', 
            'customer_number',
            'payment_method',
            'total',
            'status',
            'created_at',
            'items'
        ]

class TransactionItemCreateSerializer(serializers.ModelSerializer):
    product_id = serializers.IntegerField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2)
    
    class Meta:
        model = TransactionItem
        fields = ['product_id', 'quantity', 'price']

class TransactionCreateSerializer(serializers.ModelSerializer):
    items = TransactionItemCreateSerializer(many=True)

    class Meta:
        model = Transaction
        fields = [
            'customer_name',
            'customer_number',
            'payment_method',
            'items'
        ]

    def create(self, validated_data):
        items_data = validated_data.pop('items')
        
        total = sum(
            Decimal(str(item['quantity'])) * Decimal(str(item['price']))
            for item in items_data
        )

        transaction = Transaction.objects.create(
            total=total,
            status='COMPLETED',
            **validated_data
        )

        for item_data in items_data:
            product_id = item_data.pop('product_id')
            price = Decimal(str(item_data.pop('price')))
            quantity = Decimal(str(item_data['quantity']))
            
            try:
                product = Product.objects.get(id=product_id)
            except Product.DoesNotExist:
                raise serializers.ValidationError(f"Product with id {product_id} does not exist")

            TransactionItem.objects.create(
                transaction=transaction,
                product=product,
                price=price,
                quantity=quantity,
                subtotal=quantity * price
            )

        return transaction

    def validate(self, data):
        if not data.get('items', []):
            raise serializers.ValidationError("A transaction must have at least one item")
        return data

