{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\inventory_backend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\inventory_backend\\inventory_backend\\urls.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:inventory_backend\\urls.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\inventory_backend\\inventory_backend\\settings.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:inventory_backend\\settings.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\inventory_backend\\api\\serializers.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:api\\serializers.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\inventory_backend\\api\\urls.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:api\\urls.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\inventory_backend\\api\\models.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:api\\models.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\inventory_backend\\api\\views.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:api\\views.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\inventory_backend\\api\\utils.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:api\\utils.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\inventory_backend\\api\\admin.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:api\\admin.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\inventory_backend\\api\\apps.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:api\\apps.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\inventory_backend\\api\\tests.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:api\\tests.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\inventory_backend\\inventory_backend\\wsgi.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:inventory_backend\\wsgi.py||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 9, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1a46fd64-28d5-0019-8eb3-17a02d419b53}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 7, "Title": "admin.py", "DocumentMoniker": "C:\\Users\\<USER>\\inventory_backend\\api\\admin.py", "RelativeDocumentMoniker": "api\\admin.py", "ToolTip": "C:\\Users\\<USER>\\inventory_backend\\api\\admin.py", "RelativeToolTip": "api\\admin.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-26T10:25:36.292Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "apps.py", "DocumentMoniker": "C:\\Users\\<USER>\\inventory_backend\\api\\apps.py", "RelativeDocumentMoniker": "api\\apps.py", "ToolTip": "C:\\Users\\<USER>\\inventory_backend\\api\\apps.py", "RelativeToolTip": "api\\apps.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-05-26T10:25:25.287Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "tests.py", "DocumentMoniker": "C:\\Users\\<USER>\\inventory_backend\\api\\tests.py", "RelativeDocumentMoniker": "api\\tests.py", "ToolTip": "C:\\Users\\<USER>\\inventory_backend\\api\\tests.py", "RelativeToolTip": "api\\tests.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-03-16T07:35:23.766Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "wsgi.py", "DocumentMoniker": "C:\\Users\\<USER>\\inventory_backend\\inventory_backend\\wsgi.py", "RelativeDocumentMoniker": "inventory_backend\\wsgi.py", "ToolTip": "C:\\Users\\<USER>\\inventory_backend\\inventory_backend\\wsgi.py", "RelativeToolTip": "inventory_backend\\wsgi.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-03-16T04:53:28.041Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "utils.py", "DocumentMoniker": "C:\\Users\\<USER>\\inventory_backend\\api\\utils.py", "RelativeDocumentMoniker": "api\\utils.py", "ToolTip": "C:\\Users\\<USER>\\inventory_backend\\api\\utils.py", "RelativeToolTip": "api\\utils.py", "ViewState": "AgIAAAEAAAAAAAAAAAAAABQAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-03-16T03:30:59.09Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "urls.py", "DocumentMoniker": "C:\\Users\\<USER>\\inventory_backend\\inventory_backend\\urls.py", "RelativeDocumentMoniker": "inventory_backend\\urls.py", "ToolTip": "C:\\Users\\<USER>\\inventory_backend\\inventory_backend\\urls.py", "RelativeToolTip": "inventory_backend\\urls.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-03-16T02:08:13.9Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "urls.py", "DocumentMoniker": "C:\\Users\\<USER>\\inventory_backend\\api\\urls.py", "RelativeDocumentMoniker": "api\\urls.py", "ToolTip": "C:\\Users\\<USER>\\inventory_backend\\api\\urls.py", "RelativeToolTip": "api\\urls.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-03-16T01:26:02.132Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "views.py", "DocumentMoniker": "C:\\Users\\<USER>\\inventory_backend\\api\\views.py", "RelativeDocumentMoniker": "api\\views.py", "ToolTip": "C:\\Users\\<USER>\\inventory_backend\\api\\views.py", "RelativeToolTip": "api\\views.py", "ViewState": "AgIAAKUAAAAAAAAAAADwv7sAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-03-16T01:25:30.091Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "serializers.py", "DocumentMoniker": "C:\\Users\\<USER>\\inventory_backend\\api\\serializers.py", "RelativeDocumentMoniker": "api\\serializers.py", "ToolTip": "C:\\Users\\<USER>\\inventory_backend\\api\\serializers.py", "RelativeToolTip": "api\\serializers.py", "ViewState": "AgIAAFUAAAAAAAAAAAAcwHYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-03-16T01:25:17.944Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "models.py", "DocumentMoniker": "C:\\Users\\<USER>\\inventory_backend\\api\\models.py", "RelativeDocumentMoniker": "api\\models.py", "ToolTip": "C:\\Users\\<USER>\\inventory_backend\\api\\models.py", "RelativeToolTip": "api\\models.py", "ViewState": "AgIAADsAAAAAAAAAAAAAAEQAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-03-16T01:24:53.665Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "settings.py", "DocumentMoniker": "C:\\Users\\<USER>\\inventory_backend\\inventory_backend\\settings.py", "RelativeDocumentMoniker": "inventory_backend\\settings.py", "ToolTip": "C:\\Users\\<USER>\\inventory_backend\\inventory_backend\\settings.py", "RelativeToolTip": "inventory_backend\\settings.py", "ViewState": "AgIAAC0AAAAAAAAAAAAcwEwAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-03-16T01:23:24.374Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:283709:0:{1a46fd64-28d5-0019-8eb3-17a02d419b53}"}]}]}]}