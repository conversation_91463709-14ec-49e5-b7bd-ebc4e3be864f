from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import api_view, action
from django.db import transaction, DatabaseError
from django.db.models import Sum, Count, Avg, F
from django.db.models.functions import TruncDate
from datetime import datetime
from .models import Product, Transaction, TransactionItem, Forecast
from .serializers import (
    ProductSerializer, TransactionSerializer, 
    TransactionCreateSerializer
)
from django.db.models import Prefetch



import logging

logger = logging.getLogger(__name__)

class ProductViewSet(viewsets.ModelViewSet):
    queryset = Product.objects.all()
    serializer_class = ProductSerializer

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        try:
            with transaction.atomic():
                logger.debug(f"Received data: {request.data}")
                serializer = self.get_serializer(data=request.data)
                if serializer.is_valid():
                    self.perform_create(serializer)
                    return Response(serializer.data, status=status.HTTP_201_CREATED)
                logger.error(f"Validation errors: {serializer.errors}")
                return Response(
                    {"message": "Invalid data", "errors": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except DatabaseError as e:
            logger.error(f"Database error: {str(e)}")
            return Response(
                {"message": "Database error occurred. Please try again."},
                status=status.HTTP_503_SERVICE_UNAVAILABLE
            )

    @transaction.atomic
    def update(self, request, *args, **kwargs):
        try:
            with transaction.atomic():
                instance = self.get_object()
                # Lock the record for update
                instance = Product.objects.select_for_update(nowait=True).get(pk=instance.pk)
                
                partial = kwargs.pop('partial', False)
                serializer = self.get_serializer(instance, data=request.data, partial=partial)
                if serializer.is_valid():
                    self.perform_update(serializer)
                    return Response(serializer.data)
                # Log detailed validation errors for debugging
                logger.error(f"Validation errors on update: {serializer.errors}")
                return Response(
                    {"message": "Invalid data", "errors": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except DatabaseError as e:
            logger.error(f"Database error: {str(e)}")
            return Response(
                {"message": "Database error occurred. Please try again."},
                status=status.HTTP_503_SERVICE_UNAVAILABLE
            )

from rest_framework import viewsets, status
from rest_framework.response import Response
from django.db import transaction, DatabaseError
from django.db.models import Prefetch
import logging
from .models import Transaction, TransactionItem, Product
from .serializers import TransactionSerializer, TransactionCreateSerializer

logger = logging.getLogger(__name__)

class TransactionViewSet(viewsets.ModelViewSet):
    queryset = Transaction.objects.prefetch_related(
        Prefetch(
            'items',
            queryset=TransactionItem.objects.select_related('product')
        )
    ).order_by('-created_at')
    serializer_class = TransactionSerializer

    def get_serializer_class(self):
        if self.action == 'create':
            return TransactionCreateSerializer
        return TransactionSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        
        start_date = self.request.query_params.get('startDate')
        end_date = self.request.query_params.get('endDate')
        customer_name = self.request.query_params.get('customerName')

        if start_date:
            queryset = queryset.filter(created_at__gte=start_date)
        if end_date:
            queryset = queryset.filter(created_at__lte=end_date)
        if customer_name:
            queryset = queryset.filter(customer_name__icontains=customer_name)
            
        return queryset
    
@transaction.atomic
def create(self, request, *args, **kwargs):
    try:
        with transaction.atomic():
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                # Lock all related products
                product_ids = [item['product_id'] for item in request.data.get('items', [])]  # Changed from productId to product_id
                products = Product.objects.select_for_update(nowait=True).filter(
                    id__in=product_ids
                ).order_by('id')  # Consistent ordering to prevent deadlocks
                
                # Verify all products exist and have sufficient quantity
                if products.count() != len(product_ids):
                    return Response(
                        {"message": "One or more products not found"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                self.perform_create(serializer)
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except DatabaseError as e:
        logger.error(f"Database error in transaction creation: {str(e)}")
        return Response(
            {"message": "Service temporarily unavailable. Please try again."},
            status=status.HTTP_503_SERVICE_UNAVAILABLE
        )
            

@api_view(['GET'])
def sales_report(request):
    try:
        with transaction.atomic():
            # Get date range from query params
            start_date = request.query_params.get('startDate')
            end_date = request.query_params.get('endDate')

            # Base queryset with optimized joins
            transactions = Transaction.objects.select_related(
                'product'
            ).filter(status='COMPLETED')

            if start_date:
                transactions = transactions.filter(created_at__gte=start_date)
            if end_date:
                transactions = transactions.filter(created_at__lte=end_date)

            # Use select_related to optimize queries
            sales_data = transactions.aggregate(
                total_sales=Sum('total'),
                total_transactions=Count('id'),
                average_transaction_value=Avg('total')
            )

            # Optimize top products query
            top_products = TransactionItem.objects.select_related(
                'product'
            ).filter(
                transaction__in=transactions
            ).values(
                'product_id',
                'product__name'
            ).annotate(
                quantity=Sum('quantity'),
                revenue=Sum('subtotal')
            ).order_by('-revenue')[:5]

            daily_sales = transactions.annotate(
                date=TruncDate('created_at')
            ).values('date').annotate(
                sales=Sum('total'),
                transactions=Count('id')
            ).order_by('date')

            return Response({
                'totalSales': sales_data['total_sales'] or 0,
                'totalTransactions': sales_data['total_transactions'] or 0,
                'averageTransactionValue': sales_data['average_transaction_value'] or 0,
                'topProducts': [
                    {
                        'productId': product['product_id'],
                        'productName': product['product__name'],
                        'quantity': product['quantity'],
                        'revenue': product['revenue']
                    }
                    for product in top_products
                ],
                'dailySales': [
                    {
                        'date': item['date'].isoformat(),
                        'sales': item['sales'],
                        'transactions': item['transactions']
                    }
                    for item in daily_sales
                ]
            })
    except DatabaseError as e:
        logger.error(f"Database error in sales report: {str(e)}")
        return Response(
            {"message": "Service temporarily unavailable. Please try again."},
            status=status.HTTP_503_SERVICE_UNAVAILABLE
        )


