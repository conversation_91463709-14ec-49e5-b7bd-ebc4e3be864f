/* ProductList.css */

/* General Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Loading State */
.loading-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

/* Inventory Container */
.inventory-container {
    padding: 24px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

/* <PERSON><PERSON> and Add <PERSON> */
.mb-md {
    margin-bottom: 24px;
}

.add-button {
    text-transform: none;
    font-weight: 500;
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 40px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

    .empty-state h6 {
        margin-bottom: 8px;
        color: #666;
    }

    .empty-state p {
        color: #999;
    }

/* Table Container */
.TableContainer {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.Table {
    min-width: 600px;
}

.TableHead {
    background-color: #1976d2;
}

    .TableHead .TableCell {
        color: white;
        font-weight: 600;
    }

.TableBody .TableRow:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

.TableCell {
    padding: 16px;
}

.cell-content-wrap {
    white-space: normal;
    word-wrap: break-word;
}

.product-quantity {
    font-weight: 500;
}

.low-quantity {
    color: #d32f2f; /* Red for low quantity */
}

.product-price {
    font-weight: 500;
    color: #2
