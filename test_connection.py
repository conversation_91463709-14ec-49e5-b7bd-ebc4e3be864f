#!/usr/bin/env python3
"""
Test script to verify API connectivity from different machines
"""
import requests
import json
import sys

def test_api_endpoint(base_url, endpoint):
    """Test a specific API endpoint"""
    url = f"{base_url}{endpoint}"
    try:
        print(f"Testing: {url}")
        response = requests.get(url, timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Received {len(data) if isinstance(data, list) else 'data'}")
            if isinstance(data, list) and len(data) > 0:
                print(f"First item: {json.dumps(data[0], indent=2)[:200]}...")
            elif isinstance(data, dict):
                print(f"Response keys: {list(data.keys())}")
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
        
        print("-" * 50)
        return response.status_code == 200
        
    except requests.exceptions.ConnectTimeout:
        print(f"❌ Connection timeout to {url}")
        print("-" * 50)
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection error to {url}: {e}")
        print("-" * 50)
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print("-" * 50)
        return False

def main():
    # Test different possible base URLs
    base_urls = [
        "http://***********:8000/api/",
        "http://***************:8000/api/",
        "http://localhost:8000/api/",
        "http://127.0.0.1:8000/api/"
    ]
    
    endpoints = [
        "products/",
        "transactions/",
        "reports/sales/"
    ]
    
    print("🔍 Testing API connectivity...")
    print("=" * 60)
    
    for base_url in base_urls:
        print(f"\n🌐 Testing base URL: {base_url}")
        print("=" * 60)
        
        success_count = 0
        for endpoint in endpoints:
            if test_api_endpoint(base_url, endpoint):
                success_count += 1
        
        if success_count == len(endpoints):
            print(f"✅ ALL TESTS PASSED for {base_url}")
            print(f"🎯 Use this URL in your frontend: {base_url}")
            break
        else:
            print(f"❌ {success_count}/{len(endpoints)} tests passed for {base_url}")
    
    print("\n" + "=" * 60)
    print("📋 Frontend Configuration:")
    print("=" * 60)
    print("For React/Vite frontend, use:")
    print("const API_BASE_URL = 'http://***********:8000/api/';")
    print("\nFor axios requests:")
    print("axios.get(`${API_BASE_URL}products/`)")
    print("\nMake sure your frontend is running on:")
    print("http://***********:5173 (or the correct IP)")

if __name__ == "__main__":
    main()
