// Frontend Configuration for Inventory System
// Copy this configuration to your React/Vite frontend

// API Configuration
export const API_CONFIG = {
  // For localhost frontend (http://localhost:5173)
  BASE_URL: 'http://localhost:8000/api/',

  // Alternative URLs (uncomment if needed for network access)
  // BASE_URL: 'http://***********:8000/api/',
  // BASE_URL: 'http://***************:8000/api/',
  
  ENDPOINTS: {
    PRODUCTS: 'products/',
    TRANSACTIONS: 'transactions/',
    SALES_REPORT: 'reports/sales/',
  },
  
  // Request timeout in milliseconds
  TIMEOUT: 10000,
  
  // Headers for requests
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
};

// Axios configuration example
export const axiosConfig = {
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: API_CONFIG.HEADERS,
};

// Example usage in your React components:
/*
import axios from 'axios';
import { API_CONFIG } from './frontend_config.js';

// Create axios instance
const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: API_CONFIG.HEADERS,
});

// Fetch products
const fetchProducts = async () => {
  try {
    const response = await api.get(API_CONFIG.ENDPOINTS.PRODUCTS);
    console.log('Products:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching products:', error);
    
    // Detailed error logging
    if (error.response) {
      console.error('Response error:', error.response.status, error.response.data);
    } else if (error.request) {
      console.error('Request error:', error.request);
    } else {
      console.error('Error:', error.message);
    }
    
    throw error;
  }
};

// Fetch transactions
const fetchTransactions = async () => {
  try {
    const response = await api.get(API_CONFIG.ENDPOINTS.TRANSACTIONS);
    return response.data;
  } catch (error) {
    console.error('Error fetching transactions:', error);
    throw error;
  }
};

// Fetch sales report
const fetchSalesReport = async (startDate, endDate) => {
  try {
    const params = {};
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;
    
    const response = await api.get(API_CONFIG.ENDPOINTS.SALES_REPORT, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching sales report:', error);
    throw error;
  }
};
*/

// Network troubleshooting tips:
console.log('🔧 Network Configuration:');
console.log('Backend URL:', API_CONFIG.BASE_URL);
console.log('Frontend URL: http://localhost:5173');
console.log('');
console.log('🔍 Troubleshooting steps:');
console.log('1. Check if backend server is running on port 8000');
console.log('2. Verify firewall allows connections on port 8000');
console.log('3. Make sure both frontend and backend use localhost');
console.log('4. Check browser console for CORS errors');
console.log('5. Test API endpoints directly: http://localhost:8000/api/products/');
