/* App.css */

/* General Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Full Height Layout */
html, body, #root {
    height: 100%;
}

.flex-column {
    display: flex;
    flex-direction: column;
}

.full-height {
    height: 100%;
}

.flex-grow {
    flex-grow: 1;
}

/* AppBar Styling */
.AppBar {
    background-color: #1976d2; /* Primary color */
    color: white;
}

.Toolbar {
    padding: 0 24px;
}

/* Navigation Buttons */
.Button {
    text-transform: none; /* Prevents uppercase transformation */
    font-weight: 500;
    font-size: 1rem;
}

    .Button:hover {
        background-color: rgba(255, 255, 255, 0.1); /* Light hover effect */
    }

/* Main Content */
.content-wrapper {
    padding: 24px;
    background-color: #f5f5f5; /* Light background for content */
    border-radius: 8px;
    margin-top: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Subtle shadow */
}

/* Container */
.Container {
    padding-top: 24px;
    padding-bottom: 24px;
}

/* Footer */
footer {
    padding: 16px;
    background-color: #1976d2; /* Primary color */
    color: white;
    text-align: center;
    margin-top: auto; /* Pushes footer to the bottom */
}

    footer .Typography {
        font-size: 0.875rem;
    }

/* Responsive Design */
@media (max-width: 768px) {
    .Toolbar {
        padding: 0 16px;
    }

    .content-wrapper {
        padding: 16px;
    }

    .Container {
        padding-top: 16px;
        padding-bottom: 16px;
    }

    .Button {
        font-size: 0.875rem;
    }
}
