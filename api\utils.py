﻿from decimal import Decimal
from django.conf import settings

def format_currency(amount):
    """
    Format a number as Philippine Peso
    Example: 50.00 becomes ₱50.00
    """
    if isinstance(amount, str):
        amount = Decimal(amount.replace('$', '').replace('₱', '').strip())
    return f"₱{amount:,.2f}"

def clean_currency_input(amount):
    """
    Clean currency input by removing currency symbols and converting to Decimal
    """
    if isinstance(amount, (int, float, Decimal)):
        return Decimal(str(amount))
    
    # Remove currency symbols and whitespace
    cleaned = amount.replace('$', '').replace('₱', '').strip()
    return Decimal(cleaned)